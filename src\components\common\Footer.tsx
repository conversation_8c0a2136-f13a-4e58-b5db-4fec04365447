'use client';
import React from 'react';

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className = '' }) => {
  return (
    <footer className={`bg-global-12 py-8 sm:py-12 md:py-16 lg:py-20 ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg sm:text-xl font-semibold text-global-1 mb-4">
              Thông tin liên hệ
            </h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <img 
                  src="/images/img_location.svg" 
                  alt="Address" 
                  className="w-5 h-5 mt-1 flex-shrink-0"
                />
                <p className="text-sm sm:text-base text-global-5 leading-relaxed">
                  Địa chỉ: Tầng 3, Tòa nhà ABC, Số 123 Đường XYZ, Quận Cầu Giấy, Hà Nội
                </p>
              </div>
              <div className="flex items-center gap-3">
                <img 
                  src="/images/img_phone.svg" 
                  alt="Phone" 
                  className="w-5 h-5 flex-shrink-0"
                />
                <p className="text-sm sm:text-base text-global-5">
                  Điện thoại: +84 123 456 789
                </p>
              </div>
              <div className="flex items-center gap-3">
                <img 
                  src="/images/img_email.svg" 
                  alt="Email" 
                  className="w-5 h-5 flex-shrink-0"
                />
                <p className="text-sm sm:text-base text-global-5">
                  Email: <EMAIL>
                </p>
              </div>
              <div className="flex items-center gap-3">
                <img 
                  src="/images/img_website.svg" 
                  alt="Website" 
                  className="w-5 h-5 flex-shrink-0"
                />
                <p className="text-sm sm:text-base text-global-5">
                  Website: www.omevietnam.edu.vn
                </p>
              </div>
            </div>
          </div>

          {/* Policy Links */}
          <div className="space-y-4">
            <h3 className="text-lg sm:text-xl font-semibold text-global-1 mb-4">
              Chính sách & Quy định
            </h3>
            <div className="space-y-2">
              <a 
                href="/privacy-policy" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Chính sách bảo mật
              </a>
              <a 
                href="/terms-of-service" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Điều khoản dịch vụ
              </a>
              <a 
                href="/refund-policy" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Chính sách hoàn tiền
              </a>
              <a 
                href="/regulations" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Quy định chung
              </a>
              <a 
                href="/student-handbook" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Sổ tay học viên
              </a>
            </div>
          </div>

          {/* About Us & Certification */}
          <div className="space-y-4">
            <h3 className="text-lg sm:text-xl font-semibold text-global-1 mb-4">
              Về chúng tôi
            </h3>
            <div className="space-y-3">
              <a 
                href="/about" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Giới thiệu
              </a>
              <a 
                href="/mission" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Sứ mệnh & Tầm nhìn
              </a>
              <a 
                href="/team" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Đội ngũ giảng viên
              </a>
              <a 
                href="/achievements" 
                className="block text-sm sm:text-base text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Thành tựu
              </a>
            </div>
            
            {/* Certification Badge */}
            <div className="mt-6 pt-4 border-t border-global-13">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-global-6 rounded-lg flex items-center justify-center flex-shrink-0">
                  <img 
                    src="/images/img_certification.svg" 
                    alt="Certification" 
                    className="w-8 h-8"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-global-1">
                    Chứng nhận chất lượng
                  </p>
                  <p className="text-xs text-global-5">
                    Đạt chuẩn ISO 9001:2015
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 pt-6 border-t border-global-13">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <p className="text-sm text-global-5 text-center sm:text-left">
              © 2024 OME Vietnam Training Center. Tất cả quyền được bảo lưu.
            </p>
            <div className="flex items-center gap-4">
              <a 
                href="/sitemap" 
                className="text-sm text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Sơ đồ trang web
              </a>
              <span className="text-global-13">|</span>
              <a 
                href="/contact" 
                className="text-sm text-global-5 hover:text-global-2 transition-colors duration-200"
              >
                Liên hệ
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;