'use client';
import React, { useState } from 'react';
import Image from 'next/image';

const AppHeader: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Search query:', searchQuery);
  };

  return (
    <header className="w-full bg-[#5a4a7a] text-white">
      {/* Top Bar */}
      <div className="w-full bg-[#4a3a6a] py-2">
        <div className="max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2 text-sm">
            {/* Contact Info */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-white/80">📞 CONTACT</span>
                <span className="text-white">0966.000.643</span>
              </div>
              <div className="text-white/80">
                08:00 - 17:00
              </div>
            </div>
            
            {/* Right Side */}
            <div className="flex items-center gap-4">
              <button className="bg-red-500 hover:bg-red-600 px-3 py-1 rounded text-sm font-medium transition-colors">
                CLICK VÀO ĐỂ HỌC
              </button>
              <div className="flex items-center gap-2 text-sm">
                <span>Đăng nhập</span>
                <span>|</span>
                <span>Đăng ký</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="w-full py-4">
        <div className="max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between gap-4">
            {/* Logo */}
            <div className="flex items-center gap-2">
              <div className="w-10 h-10 bg-yellow-400 rounded-lg flex items-center justify-center">
                <span className="text-purple-800 font-bold text-lg">OM'E</span>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden lg:flex items-center gap-8">
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                Trang chủ
              </a>
              <div className="relative group">
                <button className="text-white hover:text-yellow-400 transition-colors font-medium flex items-center gap-1">
                  Giới thiệu
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
              <div className="relative group">
                <button className="text-white hover:text-yellow-400 transition-colors font-medium flex items-center gap-1">
                  Khóa học
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                Tin tức
              </a>
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                Liên hệ
              </a>
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                Sự kiện
              </a>
            </nav>

            {/* Search and Cart */}
            <div className="flex items-center gap-4">
              {/* Search */}
              <form onSubmit={handleSearch} className="hidden md:flex items-center">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Tìm kiếm khóa học..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 pr-10 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent w-64"
                  />
                  <button
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
              </form>

              {/* Cart */}
              <button className="relative bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg font-medium transition-colors">
                GIỎI GIỎ HỌC
                <span className="absolute -top-2 -right-2 bg-yellow-400 text-purple-800 text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                  0
                </span>
              </button>

              {/* Mobile Menu Button */}
              <button className="lg:hidden text-white">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
