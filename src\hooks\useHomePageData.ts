import { useState, useEffect } from 'react';
import { 
  getCategories, 
  getCourses, 
  getInstructors, 
  getArticles, 
  getTestimonials, 
  getPartners,
  isApiSuccess 
} from '@/services/dataService';
import { Course, Instructor, Article, Testimonial } from '@/types/data';

interface HomePageData {
  categories: string[];
  courses: Course[];
  instructors: Instructor[];
  articles: Article[];
  testimonials: Testimonial[];
  partners: string[];
}

interface UseHomePageDataReturn {
  data: HomePageData;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useHomePageData = (): UseHomePageDataReturn => {
  const [data, setData] = useState<HomePageData>({
    categories: [],
    courses: [],
    instructors: [],
    articles: [],
    testimonials: [],
    partners: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load all data concurrently
      const [
        categoriesResponse,
        coursesResponse,
        instructorsResponse,
        articlesResponse,
        testimonialsResponse,
        partnersResponse
      ] = await Promise.all([
        getCategories(),
        getCourses(),
        getInstructors(),
        getArticles(),
        getTestimonials(),
        getPartners()
      ]);

      // Collect any errors
      const errors: string[] = [];

      // Process responses and collect data
      const newData: HomePageData = {
        categories: [],
        courses: [],
        instructors: [],
        articles: [],
        testimonials: [],
        partners: [],
      };

      if (isApiSuccess(categoriesResponse)) {
        newData.categories = categoriesResponse.data;
      } else {
        errors.push(`Categories: ${categoriesResponse.error}`);
        console.error('Failed to load categories:', categoriesResponse.error);
      }

      if (isApiSuccess(coursesResponse)) {
        newData.courses = coursesResponse.data;
      } else {
        errors.push(`Courses: ${coursesResponse.error}`);
        console.error('Failed to load courses:', coursesResponse.error);
      }

      if (isApiSuccess(instructorsResponse)) {
        newData.instructors = instructorsResponse.data;
      } else {
        errors.push(`Instructors: ${instructorsResponse.error}`);
        console.error('Failed to load instructors:', instructorsResponse.error);
      }

      if (isApiSuccess(articlesResponse)) {
        newData.articles = articlesResponse.data;
      } else {
        errors.push(`Articles: ${articlesResponse.error}`);
        console.error('Failed to load articles:', articlesResponse.error);
      }

      if (isApiSuccess(testimonialsResponse)) {
        newData.testimonials = testimonialsResponse.data;
      } else {
        errors.push(`Testimonials: ${testimonialsResponse.error}`);
        console.error('Failed to load testimonials:', testimonialsResponse.error);
      }

      if (isApiSuccess(partnersResponse)) {
        newData.partners = partnersResponse.data;
      } else {
        errors.push(`Partners: ${partnersResponse.error}`);
        console.error('Failed to load partners:', partnersResponse.error);
      }

      // Set the data
      setData(newData);

      // If there were errors but some data loaded, show a warning
      if (errors.length > 0) {
        setError(`Some data failed to load: ${errors.join(', ')}`);
      }

    } catch (err) {
      setError('Failed to load data. Please try again later.');
      console.error('Error loading data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  return {
    data,
    isLoading,
    error,
    refetch: loadData,
  };
};
