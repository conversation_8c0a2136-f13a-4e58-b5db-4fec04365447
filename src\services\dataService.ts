import {
  Course,
  Instructor,
  Article,
  Testimonial,
  Category,
  Partner,
  ApiResponse,
  ApiError
} from '@/types/data';

// Import JSON data
import categoriesData from '@/data/categories.json';
import coursesData from '@/data/courses.json';
import instructorsData from '@/data/instructors.json';
import articlesData from '@/data/articles.json';
import testimonialsData from '@/data/testimonials.json';
import partnersData from '@/data/partners.json';

// Simulate API delay
const simulateDelay = (ms: number = 500): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};


// Generic API response wrapper
const createApiResponse = <T>(data: T): ApiResponse<T> => ({
  data,
  success: true,
  timestamp: new Date().toISOString(),
});

const createApiError = (message: string, code: number = 500): ApiError => ({
  success: false,
  error: message,
  code,
  timestamp: new Date().toISOString(),
});

// Service functions
export const getCategories = async (): Promise<ApiResponse<Category[]> | ApiError> => {
  try {
    return createApiResponse(categoriesData as Category[]);
  } catch (error) {
    return createApiError('Network error while fetching categories', 503);
  }
};

export const getCourses = async (): Promise<ApiResponse<Course[]> | ApiError> => {
  try {
    return createApiResponse(coursesData as Course[]);
  } catch (error) {
    return createApiError('Network error while fetching courses', 503);
  }
};

export const getInstructors = async (): Promise<ApiResponse<Instructor[]> | ApiError> => {
  try {
    return createApiResponse(instructorsData as Instructor[]);
  } catch (error) {
    return createApiError('Network error while fetching instructors', 503);
  }
};

export const getArticles = async (): Promise<ApiResponse<Article[]> | ApiError> => {
  try {
    return createApiResponse(articlesData as Article[]);
  } catch (error) {
    return createApiError('Network error while fetching articles', 503);
  }
};

export const getTestimonials = async (): Promise<ApiResponse<Testimonial[]> | ApiError> => {
  try {
    return createApiResponse(testimonialsData as Testimonial[]);
  } catch (error) {
    return createApiError('Network error while fetching testimonials', 503);
  }
};

export const getPartners = async (): Promise<ApiResponse<Partner[]> | ApiError> => {
  try {

    return createApiResponse(partnersData as Partner[]);
  } catch (error) {
    return createApiError('Network error while fetching partners', 503);
  }
};

// Utility function to check if response is successful
export const isApiSuccess = <T>(response: ApiResponse<T> | ApiError): response is ApiResponse<T> => {
  return response.success === true;
};

// Get course by ID
export const getCourseById = async (id: string): Promise<ApiResponse<Course | null> | ApiError> => {
  try {
    const course = coursesData.find(course => course.id === id) || null;
    return createApiResponse(course as Course | null);
  } catch (error) {
    return createApiError('Network error while fetching course', 503);
  }
};

// Get instructor by ID
export const getInstructorById = async (id: string): Promise<ApiResponse<Instructor | null> | ApiError> => {
  try {
    const instructor = instructorsData.find(instructor => instructor.id === id) || null;
    return createApiResponse(instructor as Instructor | null);
  } catch (error) {
    return createApiError('Network error while fetching instructor', 503);
  }
};

// Get article by ID
export const getArticleById = async (id: string): Promise<ApiResponse<Article | null> | ApiError> => {
  try {
    const article = articlesData.find(article => article.id === id) || null;
    return createApiResponse(article as Article | null);
  } catch (error) {
    return createApiError('Network error while fetching article', 503);
  }
};
