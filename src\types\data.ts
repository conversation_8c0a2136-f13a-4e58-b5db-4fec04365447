export interface Course {
  id: string;
  title: string;
  price: string;
  instructor: string;
  instructorImage: string;
  courseImage: string;
}

export interface Instructor {
  id: string;
  name: string;
  title: string;
  image: string;
}

export interface Article {
  id: string;
  title: string;
  excerpt: string;
  image: string;
}

export interface Testimonial {
  id: string;
  name: string;
  title: string;
  image: string;
  content: string;
}

export type Category = string;
export type Partner = string;

// API Response types to simulate real API behavior
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

export interface ApiError {
  success: false;
  error: string;
  code: number;
  timestamp: string;
}
