{"name": "hng_s_application", "version": "0.1.0", "private": true, "rocketCritical": {"dependencies": ["next", "react", "react-dom", "@dhiwise/component-tagger", "@tailwindcss/typography", "recharts"], "devDependencies": ["typescript", "@types/node", "@types/react", "@types/react-dom", "@typescript-eslint/eslint-plugin", "@typescript-eslint/parser", "eslint-config-next", "tailwindcss", "autoprefixer", "postcss", "eslint", "prettier", "@netlify/plugin-nextjs"], "scripts": ["dev", "build", "start", "lint", "lint:fix", "format", "serve", "type-check"], "warning": "🚨 CRITICAL: DO NOT REMOVE OR MODIFY ABOVE DEPENDENCIES - Required for Next.js 15 TypeScript app functionality"}, "scripts": {"dev": "next dev -p 4028", "build": "next build", "start": "next dev -p 4028", "format": "prettier --write \"src/**/*.{ts,tsx,css,md,json}\"", "serve": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.2.0", "react": "18.2.0", "react-dom": "18.2.0", "@dhiwise/component-tagger": "^1.0.10", "@tailwindcss/typography": "^0.5.16", "recharts": "^2.15.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "18.2.45", "@types/react-dom": "18.2.0", "autoprefixer": "10.4.2", "postcss": "8.4.8", "prettier": "^3.5.3", "tailwindcss": "3.4.6", "@netlify/plugin-nextjs": "^5.11.1", "typescript": "^5"}}